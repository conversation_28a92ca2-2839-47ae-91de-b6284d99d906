// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		313C470A74C04D71E4082DEF /* Pods_FitCount_FitCountUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5E85CFAF092DC4606EAED38C /* Pods_FitCount_FitCountUITests.framework */; };
		603C9289C6B97181D1CAF566 /* Pods_FitCount.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1C44BC31C64ABE73AA1B202C /* Pods_FitCount.framework */; };
		BD134A4741974D7F2F9102D7 /* Pods_FitCountTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D323C6E0E412DC8448E9342 /* Pods_FitCountTests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		1702A9ED2DE3334E0037D489 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1702A9D42DE3334C0037D489 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1702A9DB2DE3334C0037D489;
			remoteInfo = FitCount;
		};
		1702A9F72DE3334E0037D489 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1702A9D42DE3334C0037D489 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1702A9DB2DE3334C0037D489;
			remoteInfo = FitCount;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1702A9DC2DE3334C0037D489 /* FitCount.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FitCount.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1702A9EC2DE3334E0037D489 /* FitCountTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = FitCountTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1702A9F62DE3334E0037D489 /* FitCountUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = FitCountUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1C44BC31C64ABE73AA1B202C /* Pods_FitCount.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_FitCount.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1D323C6E0E412DC8448E9342 /* Pods_FitCountTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_FitCountTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		3803E1CC732C9D7206A7C2AF /* Pods-FitCount-FitCountUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FitCount-FitCountUITests.release.xcconfig"; path = "Target Support Files/Pods-FitCount-FitCountUITests/Pods-FitCount-FitCountUITests.release.xcconfig"; sourceTree = "<group>"; };
		4FA5E8C0234A744782474829 /* Pods-FitCountTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FitCountTests.debug.xcconfig"; path = "Target Support Files/Pods-FitCountTests/Pods-FitCountTests.debug.xcconfig"; sourceTree = "<group>"; };
		5BAFDA6CF54C09DB7088E5F8 /* Pods-FitCount-FitCountUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FitCount-FitCountUITests.debug.xcconfig"; path = "Target Support Files/Pods-FitCount-FitCountUITests/Pods-FitCount-FitCountUITests.debug.xcconfig"; sourceTree = "<group>"; };
		5D69753909637507446D2E25 /* Pods-FitCount.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FitCount.debug.xcconfig"; path = "Target Support Files/Pods-FitCount/Pods-FitCount.debug.xcconfig"; sourceTree = "<group>"; };
		5D98532BF4532F360A10DEFD /* Pods-FitCountTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FitCountTests.release.xcconfig"; path = "Target Support Files/Pods-FitCountTests/Pods-FitCountTests.release.xcconfig"; sourceTree = "<group>"; };
		5E5BDE76AEBB38268701F902 /* Pods-FitCount.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FitCount.release.xcconfig"; path = "Target Support Files/Pods-FitCount/Pods-FitCount.release.xcconfig"; sourceTree = "<group>"; };
		5E85CFAF092DC4606EAED38C /* Pods_FitCount_FitCountUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_FitCount_FitCountUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		1702A9DE2DE3334C0037D489 /* FitCount */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = FitCount;
			sourceTree = "<group>";
		};
		1702A9EF2DE3334E0037D489 /* FitCountTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = FitCountTests;
			sourceTree = "<group>";
		};
		1702A9F92DE3334E0037D489 /* FitCountUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = FitCountUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		1702A9D92DE3334C0037D489 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				603C9289C6B97181D1CAF566 /* Pods_FitCount.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1702A9E92DE3334E0037D489 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BD134A4741974D7F2F9102D7 /* Pods_FitCountTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1702A9F32DE3334E0037D489 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				313C470A74C04D71E4082DEF /* Pods_FitCount_FitCountUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1702A9D32DE3334C0037D489 = {
			isa = PBXGroup;
			children = (
				1702A9DE2DE3334C0037D489 /* FitCount */,
				1702A9EF2DE3334E0037D489 /* FitCountTests */,
				1702A9F92DE3334E0037D489 /* FitCountUITests */,
				1702A9DD2DE3334C0037D489 /* Products */,
				41787A67AC5F34F085AE8D9C /* Pods */,
				C8E24A55F1F256F7EC006F00 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		1702A9DD2DE3334C0037D489 /* Products */ = {
			isa = PBXGroup;
			children = (
				1702A9DC2DE3334C0037D489 /* FitCount.app */,
				1702A9EC2DE3334E0037D489 /* FitCountTests.xctest */,
				1702A9F62DE3334E0037D489 /* FitCountUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		41787A67AC5F34F085AE8D9C /* Pods */ = {
			isa = PBXGroup;
			children = (
				5D69753909637507446D2E25 /* Pods-FitCount.debug.xcconfig */,
				5E5BDE76AEBB38268701F902 /* Pods-FitCount.release.xcconfig */,
				5BAFDA6CF54C09DB7088E5F8 /* Pods-FitCount-FitCountUITests.debug.xcconfig */,
				3803E1CC732C9D7206A7C2AF /* Pods-FitCount-FitCountUITests.release.xcconfig */,
				4FA5E8C0234A744782474829 /* Pods-FitCountTests.debug.xcconfig */,
				5D98532BF4532F360A10DEFD /* Pods-FitCountTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		C8E24A55F1F256F7EC006F00 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1C44BC31C64ABE73AA1B202C /* Pods_FitCount.framework */,
				5E85CFAF092DC4606EAED38C /* Pods_FitCount_FitCountUITests.framework */,
				1D323C6E0E412DC8448E9342 /* Pods_FitCountTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1702A9DB2DE3334C0037D489 /* FitCount */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1702AA002DE3334E0037D489 /* Build configuration list for PBXNativeTarget "FitCount" */;
			buildPhases = (
				54E292087432AFD2732B6CA0 /* [CP] Check Pods Manifest.lock */,
				1702A9D82DE3334C0037D489 /* Sources */,
				1702A9D92DE3334C0037D489 /* Frameworks */,
				1702A9DA2DE3334C0037D489 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				1702A9DE2DE3334C0037D489 /* FitCount */,
			);
			name = FitCount;
			productName = FitCount;
			productReference = 1702A9DC2DE3334C0037D489 /* FitCount.app */;
			productType = "com.apple.product-type.application";
		};
		1702A9EB2DE3334E0037D489 /* FitCountTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1702AA032DE3334E0037D489 /* Build configuration list for PBXNativeTarget "FitCountTests" */;
			buildPhases = (
				58316B4431B10D04552D2C06 /* [CP] Check Pods Manifest.lock */,
				1702A9E82DE3334E0037D489 /* Sources */,
				1702A9E92DE3334E0037D489 /* Frameworks */,
				1702A9EA2DE3334E0037D489 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1702A9EE2DE3334E0037D489 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1702A9EF2DE3334E0037D489 /* FitCountTests */,
			);
			name = FitCountTests;
			productName = FitCountTests;
			productReference = 1702A9EC2DE3334E0037D489 /* FitCountTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		1702A9F52DE3334E0037D489 /* FitCountUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1702AA062DE3334E0037D489 /* Build configuration list for PBXNativeTarget "FitCountUITests" */;
			buildPhases = (
				D7E098D7ECA2266FCFFD9577 /* [CP] Check Pods Manifest.lock */,
				1702A9F22DE3334E0037D489 /* Sources */,
				1702A9F32DE3334E0037D489 /* Frameworks */,
				1702A9F42DE3334E0037D489 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1702A9F82DE3334E0037D489 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1702A9F92DE3334E0037D489 /* FitCountUITests */,
			);
			name = FitCountUITests;
			productName = FitCountUITests;
			productReference = 1702A9F62DE3334E0037D489 /* FitCountUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1702A9D42DE3334C0037D489 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					1702A9DB2DE3334C0037D489 = {
						CreatedOnToolsVersion = 16.2;
					};
					1702A9EB2DE3334E0037D489 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 1702A9DB2DE3334C0037D489;
					};
					1702A9F52DE3334E0037D489 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 1702A9DB2DE3334C0037D489;
					};
				};
			};
			buildConfigurationList = 1702A9D72DE3334C0037D489 /* Build configuration list for PBXProject "FitCount" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 1702A9D32DE3334C0037D489;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 1702A9DD2DE3334C0037D489 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1702A9DB2DE3334C0037D489 /* FitCount */,
				1702A9EB2DE3334E0037D489 /* FitCountTests */,
				1702A9F52DE3334E0037D489 /* FitCountUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1702A9DA2DE3334C0037D489 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1702A9EA2DE3334E0037D489 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1702A9F42DE3334E0037D489 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		54E292087432AFD2732B6CA0 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-FitCount-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		58316B4431B10D04552D2C06 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-FitCountTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D7E098D7ECA2266FCFFD9577 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-FitCount-FitCountUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1702A9D82DE3334C0037D489 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1702A9E82DE3334E0037D489 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1702A9F22DE3334E0037D489 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1702A9EE2DE3334E0037D489 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1702A9DB2DE3334C0037D489 /* FitCount */;
			targetProxy = 1702A9ED2DE3334E0037D489 /* PBXContainerItemProxy */;
		};
		1702A9F82DE3334E0037D489 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1702A9DB2DE3334C0037D489 /* FitCount */;
			targetProxy = 1702A9F72DE3334E0037D489 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1702A9FE2DE3334E0037D489 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1702A9FF2DE3334E0037D489 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1702AA012DE3334E0037D489 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5D69753909637507446D2E25 /* Pods-FitCount.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"FitCount/Preview Content\"";
				DEVELOPMENT_TEAM = RMX32XVT8F;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jack.FitCount;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1702AA022DE3334E0037D489 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5E5BDE76AEBB38268701F902 /* Pods-FitCount.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"FitCount/Preview Content\"";
				DEVELOPMENT_TEAM = RMX32XVT8F;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jack.FitCount;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		1702AA042DE3334E0037D489 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4FA5E8C0234A744782474829 /* Pods-FitCountTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jack.FitCountTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/FitCount.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/FitCount";
			};
			name = Debug;
		};
		1702AA052DE3334E0037D489 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5D98532BF4532F360A10DEFD /* Pods-FitCountTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jack.FitCountTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/FitCount.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/FitCount";
			};
			name = Release;
		};
		1702AA072DE3334E0037D489 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5BAFDA6CF54C09DB7088E5F8 /* Pods-FitCount-FitCountUITests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jack.FitCountUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = FitCount;
			};
			name = Debug;
		};
		1702AA082DE3334E0037D489 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3803E1CC732C9D7206A7C2AF /* Pods-FitCount-FitCountUITests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jack.FitCountUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = FitCount;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1702A9D72DE3334C0037D489 /* Build configuration list for PBXProject "FitCount" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1702A9FE2DE3334E0037D489 /* Debug */,
				1702A9FF2DE3334E0037D489 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1702AA002DE3334E0037D489 /* Build configuration list for PBXNativeTarget "FitCount" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1702AA012DE3334E0037D489 /* Debug */,
				1702AA022DE3334E0037D489 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1702AA032DE3334E0037D489 /* Build configuration list for PBXNativeTarget "FitCountTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1702AA042DE3334E0037D489 /* Debug */,
				1702AA052DE3334E0037D489 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1702AA062DE3334E0037D489 /* Build configuration list for PBXNativeTarget "FitCountUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1702AA072DE3334E0037D489 /* Debug */,
				1702AA082DE3334E0037D489 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1702A9D42DE3334C0037D489 /* Project object */;
}

//
//  ContentView.swift
//  FitCount
//
//  Created by JackFan on 2025/5/19.
//

import SwiftUI

struct ContentView: View {
    @State private var selectedTab: Tab = .first

    init() {
        // 对于自定义 TabBar，有时需要隐藏系统默认的 TabBar 背景（如果用 TabView 的话）
        // UITabBar.appearance().isHidden = true // 这个在纯 SwiftUI 自定义 TabBar 中通常不需要
    }

    var body: some View {
        ZStack(alignment: .bottom) {
            // 根据选择的标签显示不同的内容视图
            VStack {
                switch selectedTab {
                case .first:
                    ExerciseHomeView()
                case .middle:
                    MiddleView()
                case .last:
                    LastView()
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)

            // 自定义悬浮标签栏
            HStack {
                ForEach(Tab.allCases) { tab in
                    TabBarItem(tab: tab, selectedTab: $selectedTab)
                }
            }
            .padding(.horizontal) // 标签栏内部的水平边距
            .padding(.vertical, 10) // 标签栏内部的垂直边距
            .background(Material.bar) // 1. 设置背景材质
            .clipShape(RoundedRectangle(cornerRadius: 30, style: .continuous)) // 2. 然后裁剪形状
            .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 4) // 3. 再加阴影
            .padding(.horizontal, 20) // 标签栏距离屏幕边缘的边距，使其悬浮
            .padding(.bottom, 16)     // 标签栏距离屏幕底部的边距
        }
        .ignoresSafeArea(.keyboard, edges: .bottom) // 确保键盘弹出时 TabBar 行为正常
    }
}

struct TabBarItem: View {
    let tab: Tab
    @Binding var selectedTab: Tab

    var body: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.2)) { // 添加切换动画
                selectedTab = tab
            }
        }) {
            VStack(spacing: 4) {
                Image(systemName: tab.iconName)
                    .font(.system(size: tab == .middle ? 28 : 22)) // 中间图标稍大
                    .foregroundColor(selectedTab == tab ? .accentColor : Color(UIColor.systemGray))
                    .frame(width: 44, height: 44) // 保持一致的点击区域

                // 可以选择是否显示标题
                // if selectedTab == tab { 
                // Text(tab.title)
                // .font(.caption2)
                // .foregroundColor(selectedTab == tab ? .accentColor : .gray)
                // }
            }
            .frame(maxWidth: .infinity) // 让所有项目平均占据宽度
        }
        .padding(.vertical, 4) // 每个按钮的垂直内边距
    }
}

// Xcode 预览
struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}

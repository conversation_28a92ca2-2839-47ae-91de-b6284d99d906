//
//  FitCountApp.swift
//  FitCount
//
//  Created by <PERSON><PERSON><PERSON> on 2025/5/19.
//

import SwiftUI

@main
struct FitCountApp: App {

    init() {
        // 应用启动时初始化日志系统并进行测试
        setupLogging()
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }

    /// 设置日志系统
    private func setupLogging() {
        // 记录应用启动
        DebugLogger.appLaunch("FitCount 应用开始启动")

        // 在调试模式下运行日志演示
        #if DEBUG
        // 可以通过注释/取消注释来控制是否运行完整演示
        // DebugLogger.runFullDemo() // 完整演示（会产生大量日志）

        // 或者只运行基本测试
        DebugLogger.testAllLevels()
        #endif

        DebugLogger.appLaunch("FitCount 应用启动完成")
        DebugLogger.info("日志系统初始化完成，可以在 Console.app 中搜索 'com.jack.FitCount' 查看日志")
    }
}

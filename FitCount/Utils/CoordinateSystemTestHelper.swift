//
//  CoordinateSystemTestHelper.swift
//  FitCount
//
//  Created by AI Assistant on 2025-05-27.
//  用于测试和验证坐标系转换的辅助工具
//

import Foundation
import UIKit
import CoreMotion

/// 坐标系转换测试辅助工具
/// 用于验证 DeviceMotionManager 中的坐标转换逻辑是否正确
class CoordinateSystemTestHelper {

    /// 测试不同设备方向下的重力向量转换
    /// 这个方法模拟在不同设备方向下，相同的物理重力向量应该转换为相同的相机坐标系重力向量
    static func testGravityVectorConsistency() {
        DebugLogger.info("开始坐标系转换一致性测试")

        // 模拟标准重力向量（设备竖屏时，重力向下）
        let standardGravity = CMAcceleration(x: 0.0, y: -1.0, z: 0.0)

        // 测试所有设备方向
        let orientations: [UIDeviceOrientation] = [
            .portrait,
            .portraitUpsideDown,
            .landscapeLeft,
            .landscapeRight
        ]

        var results: [(UIDeviceOrientation, (x: Float, y: Float, z: Float))] = []

        for orientation in orientations {
            // 根据设备方向调整输入的重力向量
            // 这模拟了当设备旋转时，CoreMotion 报告的重力向量如何变化
            let deviceGravity = getDeviceGravityForOrientation(orientation)

            // 使用 DeviceMotionManager 的转换逻辑
            let convertedGravity = convertGravityToMediaPipeCoordinates(
                gravity: deviceGravity,
                deviceOrientation: orientation
            )

            results.append((orientation, convertedGravity))

            DebugLogger.info("设备方向: \(orientationString(orientation))")
            DebugLogger.info("  输入重力向量（设备坐标系）: (\(String(format: "%.3f", deviceGravity.x)), \(String(format: "%.3f", deviceGravity.y)), \(String(format: "%.3f", deviceGravity.z)))")
            DebugLogger.info("  输出重力向量（相机坐标系）: (\(String(format: "%.3f", convertedGravity.x)), \(String(format: "%.3f", convertedGravity.y)), \(String(format: "%.3f", convertedGravity.z)))")
        }

        // 验证一致性
        validateConsistency(results)
    }

    /// 根据设备方向获取对应的设备坐标系重力向量
    /// 这模拟了当设备旋转时，CoreMotion 如何报告重力向量
    private static func getDeviceGravityForOrientation(_ orientation: UIDeviceOrientation) -> CMAcceleration {
        switch orientation {
        case .portrait:
            // 竖屏：重力向设备下方（Y轴负方向）
            return CMAcceleration(x: 0.0, y: -1.0, z: 0.0)

        case .portraitUpsideDown:
            // 倒置竖屏：重力向设备上方（Y轴正方向）
            return CMAcceleration(x: 0.0, y: 1.0, z: 0.0)

        case .landscapeLeft:
            // 左横屏：重力向设备右侧（X轴正方向）
            return CMAcceleration(x: 1.0, y: 0.0, z: 0.0)

        case .landscapeRight:
            // 右横屏：重力向设备左侧（X轴负方向）
            return CMAcceleration(x: -1.0, y: 0.0, z: 0.0)

        default:
            return CMAcceleration(x: 0.0, y: -1.0, z: 0.0)
        }
    }

    /// 复制 DeviceMotionManager 中的坐标转换逻辑用于测试
    private static func convertGravityToMediaPipeCoordinates(
        gravity: CMAcceleration,
        deviceOrientation: UIDeviceOrientation
    ) -> (x: Float, y: Float, z: Float) {

        switch deviceOrientation {
        case .portrait:
            // 竖屏 → UIImage.Orientation.up
            return (
                x: Float(gravity.x),   // 设备左右 → 图像左右
                y: Float(gravity.y),   // 设备上下 → 图像上下（MediaPipe Y轴向下）
                z: Float(gravity.z)    // 设备前后 → 图像前后
            )

        case .portraitUpsideDown:
            // 倒置竖屏（实际回退到竖屏）
            return (
                x: Float(gravity.x),
                y: Float(gravity.y),
                z: Float(gravity.z)
            )

        case .landscapeLeft:
            // 左横屏 → UIImage.Orientation.left
            return (
                x: Float(-gravity.y),  // 设备上下反转 → 图像左右
                y: Float(gravity.x),   // 设备左右 → 图像上下
                z: Float(gravity.z)    // 设备前后 → 图像前后
            )

        case .landscapeRight:
            // 右横屏 → UIImage.Orientation.right
            return (
                x: Float(gravity.y),   // 设备上下 → 图像左右
                y: Float(-gravity.x),  // 设备左右反转 → 图像上下
                z: Float(gravity.z)    // 设备前后 → 图像前后
            )

        default:
            return (
                x: Float(gravity.x),
                y: Float(gravity.y),
                z: Float(gravity.z)
            )
        }
    }

    /// 验证转换结果的一致性
    private static func validateConsistency(_ results: [(UIDeviceOrientation, (x: Float, y: Float, z: Float))]) {
        DebugLogger.info("验证转换结果一致性...")

        // 新的期望结果：
        // 在 MediaPipe worldLandmarks 坐标系中，重力向量应该在所有方向下都表示相同的物理方向
        // 但由于 MediaPipe 基于旋转后的图像，重力向量会根据图像旋转而变化
        //
        // 期望结果：
        // - 竖屏：(0, -1, 0) - 重力向下（Y轴负方向，因为设备重力向下）
        // - 左横屏：(1, 0, 0) - 重力向右（因为图像逆时针旋转90度）
        // - 右横屏：(-1, 0, 0) - 重力向左（因为图像顺时针旋转90度）

        let expectedResults: [UIDeviceOrientation: (x: Float, y: Float, z: Float)] = [
            .portrait: (x: 0.0, y: -1.0, z: 0.0),        // 竖屏：重力向下
            .portraitUpsideDown: (x: 0.0, y: -1.0, z: 0.0), // 倒置竖屏（回退到竖屏）
            .landscapeLeft: (x: 1.0, y: 0.0, z: 0.0),    // 左横屏：重力向右
            .landscapeRight: (x: -1.0, y: 0.0, z: 0.0)   // 右横屏：重力向左
        ]

        let tolerance: Float = 0.001
        var allConsistent = true

        for (orientation, gravity) in results {
            guard let expected = expectedResults[orientation] else {
                DebugLogger.warning("⚠️ 未定义期望结果的方向: \(orientationString(orientation))")
                continue
            }

            let xDiff = abs(gravity.x - expected.x)
            let yDiff = abs(gravity.y - expected.y)
            let zDiff = abs(gravity.z - expected.z)

            let isConsistent = xDiff < tolerance && yDiff < tolerance && zDiff < tolerance

            if isConsistent {
                DebugLogger.info("✅ \(orientationString(orientation)): 转换正确")
            } else {
                DebugLogger.error("❌ \(orientationString(orientation)): 转换不正确")
                DebugLogger.error("  期望: (\(expected.x), \(expected.y), \(expected.z))")
                DebugLogger.error("  实际: (\(gravity.x), \(gravity.y), \(gravity.z))")
                DebugLogger.error("  差异: (\(xDiff), \(yDiff), \(zDiff))")
                allConsistent = false
            }
        }

        if allConsistent {
            DebugLogger.info("🎉 所有设备方向的坐标转换都正确！")
            DebugLogger.info("💡 现在相同的身体姿势在不同设备方向下应该计算出相同的角度")
        } else {
            DebugLogger.error("⚠️ 存在坐标转换错误，需要修复")
        }
    }

    /// 获取设备方向的字符串描述
    private static func orientationString(_ orientation: UIDeviceOrientation) -> String {
        switch orientation {
        case .portrait:
            return "竖屏"
        case .portraitUpsideDown:
            return "倒置竖屏"
        case .landscapeLeft:
            return "左横屏"
        case .landscapeRight:
            return "右横屏"
        case .faceUp:
            return "屏幕向上"
        case .faceDown:
            return "屏幕向下"
        case .unknown:
            return "未知方向"
        @unknown default:
            return "未知方向"
        }
    }
}

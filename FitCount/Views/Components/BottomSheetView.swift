import SwiftUI

// MARK: - 底部设置面板组件

/// 底部设置面板 - 使用 sheet 和 presentationDetents 来实现
struct BottomSheetView: View {
    
    // MARK: - 内部类型定义
    
    /// 控制面板采用的停靠位置
    enum DetentHeight {
        case small     // 小尺寸，约为屏幕高度的10%
        case medium    // 中等尺寸，约为屏幕高度的50%
        case large     // 大尺寸，约为屏幕高度的100%
    }
    
    // MARK: - 属性
    
    /// 控制面板关闭的状态绑定
    @Binding var isPresented: Bool
    
    /// 当前选中的停靠位置
    @State private var selectedDetent: DetentHeight = .medium
    
    // MARK: - 视图主体
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 0) {
                    // 拖动指示器
                    DragIndicator()
                        .padding(.vertical, 8)
                    
                    // 设置内容区域
                    settingsContent
                }
                .padding(.horizontal)
            }
            .navigationTitle(LocalizedStringKey("设置选项"))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                // 关闭按钮
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        isPresented = false
                    }) {
                        Text(LocalizedStringKey("完成"))
                            .fontWeight(.bold)
                    }
                }
                
                // 面板大小切换按钮
                ToolbarItem(placement: .navigationBarLeading) {
                    Menu {
                        Button(action: { selectedDetent = .small }) {
                            Label(LocalizedStringKey("小"), systemImage: selectedDetent == .small ? "checkmark" : "")
                        }
                        
                        Button(action: { selectedDetent = .medium }) {
                            Label(LocalizedStringKey("中"), systemImage: selectedDetent == .medium ? "checkmark" : "")
                        }
                        
                        Button(action: { selectedDetent = .large }) {
                            Label(LocalizedStringKey("大"), systemImage: selectedDetent == .large ? "checkmark" : "")
                        }
                    } label: {
                        Image(systemName: "arrow.up.and.down")
                    }
                }
            }
            .background(Color(UIColor.systemGroupedBackground))
        }
        .presentationDetents([
            .fraction(0.1),   // 小：10%屏幕高度
            .fraction(0.5),   // 中：50%屏幕高度
            .fraction(0.95)   // 大：95%屏幕高度
        ], selection: detentSelection)
        .presentationDragIndicator(.visible) // 显示系统拖动指示器
        .interactiveDismissDisabled() // 禁止交互式关闭（必须点击完成按钮）
    }
    
    // MARK: - 私有视图组件
    
    /// 设置面板主要内容
    private var settingsContent: some View {
        VStack(spacing: 20) {
            // 动作识别设置区域
            settingsSection(title: "动作识别") {
                toggleItem("启用动作识别", isOn: true)
                toggleItem("显示计数", isOn: true)
            }
            
            // 通知设置区域
            settingsSection(title: "通知设置") {
                toggleItem("声音提示", isOn: false)
            }
            
            // 显示设置区域
            settingsSection(title: "显示设置") {
                toggleItem("显示网格线", isOn: false)
                toggleItem("自动保存", isOn: true)
                toggleItem("无屏蔽模式", isOn: false)
            }
            
            // 关于区域
            settingsSection(title: "关于") {
                HStack {
                    Text(LocalizedStringKey("版本"))
                    Spacer()
                    Text("1.0.0")
                        .foregroundColor(.gray)
                }
                .padding(.vertical, 8)
            }
            
            // 底部空白，确保内容可滚动
            Spacer(minLength: 40)
        }
    }
    
    /// 创建设置区域
    private func settingsSection<Content: View>(title: String, @ViewBuilder content: () -> Content) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(LocalizedStringKey(title))
                .font(.headline)
                .padding(.leading, 5)
            
            VStack(spacing: 0) {
                content()
            }
            .padding(.vertical, 5)
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
        }
    }
    
    /// 创建开关项
    private func toggleItem(_ title: String, isOn: Bool) -> some View {
        Toggle(LocalizedStringKey(title), isOn: .constant(isOn))
            .toggleStyle(SwitchToggleStyle(tint: .blue))
            .padding(.vertical, 8)
            .padding(.horizontal, 15)
    }
    
    /// 将选择的停靠高度转换为绑定值
    private var detentSelection: Binding<PresentationDetent> {
        Binding<PresentationDetent> {
            switch selectedDetent {
            case .small: return .fraction(0.1)
            case .medium: return .fraction(0.5)
            case .large: return .fraction(0.95)
            }
        } set: { newValue in
            switch newValue {
            case .fraction(0.1): selectedDetent = .small
            case .fraction(0.5): selectedDetent = .medium
            case .fraction(0.95): selectedDetent = .large
            default: selectedDetent = .medium
            }
        }
    }
}

// MARK: - 辅助组件

/// 自定义拖动指示器
struct DragIndicator: View {
    var body: some View {
        Capsule()
            .fill(Color.gray.opacity(0.5))
            .frame(width: 40, height: 5)
    }
}

// MARK: - 预览

struct BottomSheetView_Previews: PreviewProvider {
    static var previews: some View {
        BottomSheetView(isPresented: .constant(true))
            .environment(\.locale, .init(identifier: "zh-Hans"))
    }
}

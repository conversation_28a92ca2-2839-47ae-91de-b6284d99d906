import SwiftUI

/// 设备运动状态显示视图
/// 显示设备运动传感器的状态和实时数据
struct DeviceMotionStatusView: View {

    // MARK: - 属性

    /// 仰卧起坐计数器引用
    let sitUpCounter: SitUpCounter

    /// 定时器，用于定期更新显示
    @State private var timer: Timer?

    /// 设备运动状态
    @State private var motionInfo: (isAvailable: Bool, isActive: Bool, angles: (pitch: Double, roll: Double, yaw: Double)?) = (false, false, nil)

    // MARK: - 视图主体

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 传感器可用性状态
            HStack {
                Image(systemName: motionInfo.isAvailable ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(motionInfo.isAvailable ? .green : .red)

                Text(NSLocalizedString("settings_sensor_availability", comment: "传感器可用性"))
                    .font(.body)

                Spacer()

                Text(motionInfo.isAvailable ?
                     NSLocalizedString("device_motion_sensor_available", comment: "可用") :
                     NSLocalizedString("device_motion_sensor_unavailable", comment: "不可用"))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // 传感器运行状态
            HStack {
                Image(systemName: motionInfo.isActive ? "play.circle.fill" : "pause.circle.fill")
                    .foregroundColor(motionInfo.isActive ? .blue : .orange)

                Text(NSLocalizedString("settings_sensor_status", comment: "传感器状态"))
                    .font(.body)

                Spacer()

                Text(motionInfo.isActive ?
                     NSLocalizedString("device_motion_sensor_active", comment: "运行中") :
                     NSLocalizedString("device_motion_sensor_inactive", comment: "已停止"))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // 设备姿态角度（如果可用）
            if let angles = motionInfo.angles {
                Divider()

                VStack(alignment: .leading, spacing: 8) {
                    Text(NSLocalizedString("settings_device_attitude_angles", comment: "设备姿态角度"))
                        .font(.headline)
                        .foregroundColor(.primary)

                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(NSLocalizedString("device_motion_pitch", comment: "俯仰角"))
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(angles.pitch, specifier: "%.1f")°")
                                .font(.body)
                                .fontWeight(.medium)
                        }

                        Spacer()

                        VStack(alignment: .leading, spacing: 4) {
                            Text(NSLocalizedString("device_motion_roll", comment: "翻滚角"))
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(angles.roll, specifier: "%.1f")°")
                                .font(.body)
                                .fontWeight(.medium)
                        }

                        Spacer()

                        VStack(alignment: .leading, spacing: 4) {
                            Text(NSLocalizedString("device_motion_yaw", comment: "偏航角"))
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(angles.yaw, specifier: "%.1f")°")
                                .font(.body)
                                .fontWeight(.medium)
                        }
                    }
                }

                // 设备倾斜状态指示器
                DeviceTiltIndicator(pitch: angles.pitch, roll: angles.roll)
            }
        }
        .padding(.vertical, 8)
        .onAppear {
            startUpdating()
        }
        .onDisappear {
            stopUpdating()
        }
    }

    // MARK: - 私有方法

    /// 开始定期更新设备运动信息
    private func startUpdating() {
        // 立即更新一次
        updateMotionInfo()

        // 设置定时器，每0.5秒更新一次
        timer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
            updateMotionInfo()
        }
    }

    /// 停止更新
    private func stopUpdating() {
        timer?.invalidate()
        timer = nil
    }

    /// 更新设备运动信息
    private func updateMotionInfo() {
        motionInfo = sitUpCounter.getDeviceMotionInfo()
    }
}

/// 设备倾斜状态指示器
struct DeviceTiltIndicator: View {
    let pitch: Double
    let roll: Double

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(NSLocalizedString("settings_device_tilt_status", comment: "设备倾斜状态"))
                .font(.caption)
                .foregroundColor(.secondary)

            HStack(spacing: 16) {
                // 俯仰倾斜指示器
                VStack(spacing: 4) {
                    Text(NSLocalizedString("device_tilt_direction_forward_backward", comment: "前后倾斜"))
                        .font(.caption2)
                        .foregroundColor(.secondary)

                    ZStack {
                        Rectangle()
                            .fill(Color.gray.opacity(0.2))
                            .frame(width: 60, height: 20)
                            .cornerRadius(4)

                        Rectangle()
                            .fill(getTiltColor(for: pitch))
                            .frame(width: 4, height: 16)
                            .cornerRadius(2)
                            .offset(x: CGFloat(pitch / 90.0 * 28)) // 最大偏移28点
                    }
                }

                // 翻滚倾斜指示器
                VStack(spacing: 4) {
                    Text(NSLocalizedString("device_tilt_direction_left_right", comment: "左右倾斜"))
                        .font(.caption2)
                        .foregroundColor(.secondary)

                    ZStack {
                        Rectangle()
                            .fill(Color.gray.opacity(0.2))
                            .frame(width: 60, height: 20)
                            .cornerRadius(4)

                        Rectangle()
                            .fill(getTiltColor(for: roll))
                            .frame(width: 4, height: 16)
                            .cornerRadius(2)
                            .offset(x: CGFloat(roll / 90.0 * 28)) // 最大偏移28点
                    }
                }

                Spacer()

                // 倾斜程度文字描述
                VStack(alignment: .trailing, spacing: 2) {
                    Text(getTiltDescription(pitch: pitch, roll: roll))
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(getTiltColor(for: max(abs(pitch), abs(roll))))

                    Text(String(format: NSLocalizedString("device_tilt_degree_format", comment: "倾斜度: %.1f°"), max(abs(pitch), abs(roll))))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.top, 8)
    }

    /// 根据倾斜角度获取颜色
    private func getTiltColor(for angle: Double) -> Color {
        let absAngle = abs(angle)
        switch absAngle {
        case 0..<5:
            return .green
        case 5..<15:
            return .yellow
        case 15..<30:
            return .orange
        default:
            return .red
        }
    }

    /// 获取倾斜状态描述
    private func getTiltDescription(pitch: Double, roll: Double) -> String {
        let maxTilt = max(abs(pitch), abs(roll))

        switch maxTilt {
        case 0..<5:
            return NSLocalizedString("device_tilt_level", comment: "水平")
        case 5..<15:
            return NSLocalizedString("device_tilt_slight", comment: "轻微倾斜")
        case 15..<30:
            return NSLocalizedString("device_tilt_moderate", comment: "中度倾斜")
        case 30..<60:
            return NSLocalizedString("device_tilt_significant", comment: "大幅倾斜")
        default:
            return NSLocalizedString("device_tilt_severe", comment: "严重倾斜")
        }
    }
}

// MARK: - 预览

struct DeviceMotionStatusView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            List {
                Section {
                    DeviceMotionStatusView(sitUpCounter: SitUpCounter())
                } header: {
                    Text("设备运动状态")
                } footer: {
                    Text("设备运动传感器用于精确计算重力方向，提高角度检测准确性")
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("设置")
        }
    }
}

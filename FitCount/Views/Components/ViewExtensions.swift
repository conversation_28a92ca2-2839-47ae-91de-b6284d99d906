import SwiftUI
import UIKit

// MARK: - 视图扩展和工具类

// MARK: - 屏幕方向自适应

/// 用于处理屏幕方向变化的扩展视图修饰符
struct OrientationAdaptiveModifier: ViewModifier {
    let horizontalSizeClass: UserInterfaceSizeClass?
    
    func body(content: Content) -> some View {
        if horizontalSizeClass == .regular {
            // 横屏布局
            content
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        } else {
            // 竖屏布局
            content
        }
    }
}

// MARK: - 自定义圆角形状

/// 自定义圆角形状 - 支持指定特定角落为圆角
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners
    
    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// MARK: - View 扩展

extension View {
    
    /// 屏幕方向自适应修饰符
    /// - Parameter horizontalSizeClass: 水平尺寸类别
    /// - Returns: 应用了方向自适应修饰符的视图
    func adaptToScreenOrientation(horizontalSizeClass: UserInterfaceSizeClass?) -> some View {
        self.modifier(OrientationAdaptiveModifier(horizontalSizeClass: horizontalSizeClass))
    }
    
    /// 自定义圆角修饰符 - 支持指定特定角落
    /// - Parameters:
    ///   - radius: 圆角半径
    ///   - corners: 需要圆角的角落
    /// - Returns: 应用了自定义圆角的视图
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

// MARK: - 本地化字符串扩展

/// 本地化字符串键值定义
/// 用于支持iOS国际化标准
extension String {
    
    // MARK: - 相机相关
    static let cameraToggle = "切换摄像头"
    static let cameraError = "相机错误"
    static let cameraInitializing = "摄像头初始化中..."
    static let cameraInterrupted = "相机访问已中断"
    static let restartCamera = "重新启动相机"
    static let checkCameraStatus = "检查相机状态"
    
    // MARK: - 设置相关
    static let settingsTitle = "设置选项"
    static let enableRecognition = "启用动作识别"
    static let showCounter = "显示计数"
    static let soundAlert = "声音提示"
    
    // MARK: - 通用按钮
    static let okButton = "确定"
    static let backButton = "返回"
    static let completeButton = "完成"
    
    // MARK: - 错误信息
    static let errorPrefix = "错误: "
    
    // MARK: - 尺寸选项
    static let sizeSmall = "小"
    static let sizeMedium = "中"
    static let sizeLarge = "大"
    
    // MARK: - 设置选项
    static let motionRecognition = "动作识别"
    static let notificationSettings = "通知设置"
    static let displaySettings = "显示设置"
    static let aboutSection = "关于"
    static let version = "版本"
    
    // MARK: - 显示选项
    static let showGridLines = "显示网格线"
    static let autoSave = "自动保存"
    static let noBlockMode = "无屏蔽模式"
}

// MARK: - 本地化字符串资源

/// 本地化字符串资源管理
/// 确保所有用户可见的文本都符合iOS国际化标准
struct LocalizedStrings {
    
    // MARK: - 相机相关本地化
    struct Camera {
        static let toggle = NSLocalizedString("camera.toggle", value: "切换摄像头", comment: "切换前后摄像头按钮")
        static let error = NSLocalizedString("camera.error", value: "相机错误", comment: "相机错误标题")
        static let initializing = NSLocalizedString("camera.initializing", value: "摄像头初始化中...", comment: "相机初始化状态")
        static let interrupted = NSLocalizedString("camera.interrupted", value: "相机访问已中断", comment: "相机被中断提示")
        static let restart = NSLocalizedString("camera.restart", value: "重新启动相机", comment: "重启相机按钮")
        static let checkStatus = NSLocalizedString("camera.check_status", value: "检查相机状态", comment: "检查相机状态按钮")
    }
    
    // MARK: - 设置相关本地化
    struct Settings {
        static let title = NSLocalizedString("settings.title", value: "设置选项", comment: "设置页面标题")
        static let enableRecognition = NSLocalizedString("settings.enable_recognition", value: "启用动作识别", comment: "启用动作识别开关")
        static let showCounter = NSLocalizedString("settings.show_counter", value: "显示计数", comment: "显示计数开关")
        static let soundAlert = NSLocalizedString("settings.sound_alert", value: "声音提示", comment: "声音提示开关")
        static let showGridLines = NSLocalizedString("settings.show_grid_lines", value: "显示网格线", comment: "显示网格线开关")
        static let autoSave = NSLocalizedString("settings.auto_save", value: "自动保存", comment: "自动保存开关")
        static let noBlockMode = NSLocalizedString("settings.no_block_mode", value: "无屏蔽模式", comment: "无屏蔽模式开关")
    }
    
    // MARK: - 通用按钮本地化
    struct Buttons {
        static let ok = NSLocalizedString("button.ok", value: "确定", comment: "确定按钮")
        static let back = NSLocalizedString("button.back", value: "返回", comment: "返回按钮")
        static let complete = NSLocalizedString("button.complete", value: "完成", comment: "完成按钮")
        static let cancel = NSLocalizedString("button.cancel", value: "取消", comment: "取消按钮")
    }
    
    // MARK: - 区域标题本地化
    struct Sections {
        static let motionRecognition = NSLocalizedString("section.motion_recognition", value: "动作识别", comment: "动作识别设置区域")
        static let notificationSettings = NSLocalizedString("section.notification_settings", value: "通知设置", comment: "通知设置区域")
        static let displaySettings = NSLocalizedString("section.display_settings", value: "显示设置", comment: "显示设置区域")
        static let about = NSLocalizedString("section.about", value: "关于", comment: "关于区域")
        static let version = NSLocalizedString("section.version", value: "版本", comment: "版本信息")
    }
    
    // MARK: - 尺寸选项本地化
    struct Sizes {
        static let small = NSLocalizedString("size.small", value: "小", comment: "小尺寸选项")
        static let medium = NSLocalizedString("size.medium", value: "中", comment: "中等尺寸选项")
        static let large = NSLocalizedString("size.large", value: "大", comment: "大尺寸选项")
    }
}

// MARK: - 调试工具扩展

#if DEBUG
extension View {
    /// 调试边框 - 仅在调试模式下可用
    /// - Parameter color: 边框颜色，默认为红色
    /// - Returns: 添加了调试边框的视图
    func debugBorder(_ color: Color = .red) -> some View {
        self.border(color, width: 1)
    }
    
    /// 调试背景 - 仅在调试模式下可用
    /// - Parameter color: 背景颜色，默认为半透明红色
    /// - Returns: 添加了调试背景的视图
    func debugBackground(_ color: Color = .red.opacity(0.3)) -> some View {
        self.background(color)
    }
}
#endif

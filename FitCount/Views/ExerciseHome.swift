import SwiftUI
import AVFoundation

// 本地化字符串键
extension LocalizedStringKey {
    // 运动类型
    static let exerciseTitle = LocalizedStringKey("exercise")
    static let exerciseSelectionDescription = LocalizedStringKey("exercise.selection.description")
    
    // 具体运动项目
    static let exerciseSitup = LocalizedStringKey("exercise.situp")
    static let exercisePullup = LocalizedStringKey("exercise.pullup")
    static let exercisePushup = LocalizedStringKey("exercise.pushup")
    static let exerciseSquat = LocalizedStringKey("exercise.squat")
    static let exercisePlank = LocalizedStringKey("exercise.plank")
    
    // 相机权限
    static let cameraPermissionTitle = LocalizedStringKey("camera.permission.title")
    static let cameraPermissionMessage = LocalizedStringKey("camera.permission.message")
    static let cameraPermissionOk = LocalizedStringKey("camera.permission.ok")
}

struct ExerciseItem: Identifiable {
    var id = UUID()
    var name: String
}

struct ExerciseHomeView: View {
    @State private var selectedExercise: ExerciseItem?
    @State private var isShowingDetail: Bool = false
    @State private var showCameraPermissionAlert = false
    
    let exercises = [
        ExerciseItem(name: "exercise.situp"),
        ExerciseItem(name: "exercise.pullup"),
        ExerciseItem(name: "exercise.pushup"),
        ExerciseItem(name: "exercise.squat"),
        ExerciseItem(name: "exercise.plank")
    ]
    
    var body: some View {
        NavigationView {
            VStack(alignment: .center, spacing: 20) {
                Text(.exerciseTitle)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top)
                
                Text(.exerciseSelectionDescription)
                    .font(.subheadline)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                ScrollView {
                    VStack(spacing: 15) {
                        ForEach(exercises) { exercise in
                            Button(action: {
                                selectedExercise = exercise
                                checkCameraPermission()
                            }) {
                                // 根据字符串键创建本地化 Text
                                Text(LocalizedStringKey(exercise.name))
                                    .font(.title3)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 60)
                                    .background(
                                        RoundedRectangle(cornerRadius: 15)
                                            .stroke(Color.black, lineWidth: 2)
                                    )
                                    .padding(.horizontal)
                            }
                            .foregroundColor(.black)
                        }
                    }
                    .padding(.vertical)
                }
                
                Spacer()
            }
            .navigationBarHidden(true)
            .fullScreenCover(isPresented: $isShowingDetail) {
                NavigationView {
                    ExerciseSitupView(exerciseName: selectedExercise?.name ?? "")
                }
            }
        }
        .alert(isPresented: $showCameraPermissionAlert) {
            Alert(
                title: Text(.cameraPermissionTitle),
                message: Text(.cameraPermissionMessage),
                dismissButton: .default(Text(.cameraPermissionOk))
            )
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }
    
    private func checkCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized: // The user has previously granted access to the camera
            isShowingDetail = true
            
        case .notDetermined: // The user has not yet been asked for camera access
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    if granted {
                        isShowingDetail = true
                    } else {
                        showCameraPermissionAlert = true
                    }
                }
            }
            
        case .denied, .restricted: // The user can't grant access due to restrictions or has previously denied access
            showCameraPermissionAlert = true
            
        @unknown default:
            break
        }
    }
}

struct ExerciseView_Previews: PreviewProvider {
    static var previews: some View {
        ExerciseHomeView()
            .environment(\.locale, .init(identifier: "en"))
        ExerciseHomeView()
            .environment(\.locale, .init(identifier: "zh-Hans"))
            .previewDisplayName("First View - Chinese")
    }
} 

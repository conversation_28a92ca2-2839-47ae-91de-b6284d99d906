/* FitCount English Localizations */

"tab.first.title" = "Module One";
"tab.middle.title" = "Core Feature";
"tab.last.title" = "Module Two";

"view.first.content" = "This is the content area for the first module.";
"view.middle.content" = "This is the content area for the core feature module.";
"view.last.content" = "This is the content area for the second module.";

"view.first.button.goToDetail" = "Go to Detail Page";

// Detail View Localizations
"view.first.detail.title" = "Detail Page";
"view.first.detail.content" = "This is the full-screen detail content from the first module.";
"view.first.detail.navigationTitle" = "Module One Detail";
"button.done" = "Done";

// FirstView Localizations
"exercise" = "Exercise";
"exercise.selection.description" = "Select the exercise you want to do. All data stays on your device only.";

// Exercise Items
"exercise.situp" = "Sit-ups";
"exercise.pullup" = "Pull-ups";
"exercise.pushup" = "Push-ups";
"exercise.squat" = "Squats";
"exercise.plank" = "Plank";
"error.prefix" = "Error: ";

// Camera Permission Alert
"camera.permission.title" = "Camera Permission Required";
"camera.permission.message" = "Please enable camera access in Settings to use this feature";
"camera.permission.ok" = "OK";

// Device Orientation and Coordinate Transform
"orientation.portrait" = "Portrait";
"orientation.landscape.left" = "Landscape Left";
"orientation.landscape.right" = "Landscape Right";
"orientation.unknown" = "Unknown Orientation";
"pose.overlay.coordinate.transform" = "Pose Coordinate Transform";
"pose.overlay.orientation.changed" = "Device Orientation Changed";

// MARK: - Sit-up Counter Related Text

/* Sit-up Counter States */
"situp_counter_ready" = "Ready to Start";
"situp_counter_lying_down" = "Lying Down";
"situp_counter_sitting_up" = "Sitting Up";
"situp_counter_completed" = "Completed";

/* Sit-up Count Display */
"situp_count_label" = "Sit-ups: %d";
"situp_angle_label" = "Angle: %.1f°";
"situp_status_label" = "Status: %@";

/* Sit-up Hint Messages */
"situp_hint_position" = "Please lie down with knees bent";
"situp_hint_start" = "Start sit-up motion";
"situp_hint_continue" = "Keep the motion going";
"situp_hint_good" = "Good form!";

/* Error Messages */
"situp_error_no_landmarks" = "No body landmarks detected";
"situp_error_insufficient_landmarks" = "Insufficient landmark data";
"situp_error_invalid_pose" = "Invalid pose";

// MARK: - Device Motion Sensor Related Text

/* Device Motion Status */
"device_motion_sensor_available" = "Sensor Available";
"device_motion_sensor_unavailable" = "Sensor Unavailable";
"device_motion_sensor_active" = "Active";
"device_motion_sensor_inactive" = "Inactive";

/* Device Attitude Angles */
"device_motion_pitch" = "Pitch";
"device_motion_roll" = "Roll";
"device_motion_yaw" = "Yaw";

/* Device Tilt Status */
"device_tilt_level" = "Level";
"device_tilt_slight" = "Slight Tilt";
"device_tilt_moderate" = "Moderate Tilt";
"device_tilt_significant" = "Significant Tilt";
"device_tilt_severe" = "Severe Tilt";

/* Device Tilt Direction */
"device_tilt_direction_forward_backward" = "Forward/Backward Tilt";
"device_tilt_direction_left_right" = "Left/Right Tilt";
"device_tilt_degree_format" = "Tilt: %.1f°";

/* Device Motion Manager Logs */
"device_motion_manager_started" = "Device Motion Manager Started";
"device_motion_manager_stopped" = "Device Motion Manager Stopped";
"device_motion_data_available" = "Device Motion Data Available";
"device_motion_data_unavailable" = "Device Motion Data Unavailable";
"device_motion_precise_gravity_available" = "Using Precise Gravity Vector";
"device_motion_precise_gravity_unavailable" = "Precise gravity vector unavailable, using standard gravity vector";

/* Coordinate System and Angle Calculation Related */
"coordinate_system_world_landmarks" = "MediaPipe World Coordinate System";
"coordinate_system_device_coordinates" = "Device Coordinate System";
"gravity_vector_standard" = "Standard Gravity Vector";
"gravity_vector_precise" = "Precise Gravity Vector";
"angle_calculation_consistent" = "Angle Calculation Consistency Fix";
"device_orientation_independence" = "Device Orientation Independence";
"torso_angle_calculation" = "Torso Angle Calculation";
"pose_detection_accuracy" = "Pose Detection Accuracy";

/* Angle Calculation Correction Related */
"torso_inclination_angle" = "Torso Inclination Angle";
"horizontal_reference" = "Horizontal Reference";
"vertical_reference" = "Vertical Reference";
"angle_definition_horizontal" = "Angle Definition: Relative to Horizontal Plane";
"lying_down_horizontal" = "Horizontal When Lying Down";
"standing_up_vertical" = "Vertical When Standing Up";
"angle_calculation_method" = "Angle Calculation Method";
"inclination_measurement" = "Inclination Measurement";

/* Settings Page */
"settings_device_motion_section_title" = "Device Motion Status";
"settings_device_motion_section_footer" = "Device motion sensors are used to precisely calculate gravity direction for improved angle detection accuracy";
"settings_sensor_availability" = "Sensor Availability";
"settings_sensor_status" = "Sensor Status";
"settings_device_attitude_angles" = "Device Attitude Angles";
"settings_device_tilt_status" = "Device Tilt Status";